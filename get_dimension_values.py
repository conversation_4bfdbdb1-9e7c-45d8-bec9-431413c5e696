#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简洁的DXF尺寸标注数值提取工具
专门用于快速获取尺寸标注的数值
"""

import sys
from pathlib import Path
import json

try:
    import ezdxf
except ImportError:
    print("请先安装ezdxf库: pip install ezdxf")
    sys.exit(1)


def extract_dimension_values(file_path: str):
    """从DXF文件提取尺寸标注数值"""
    try:
        # 加载DXF文件
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        
        dimensions = []
        
        for entity in msp:
            if entity.dxftype() == 'DIMENSION':
                # 尝试计算尺寸值
                value = None
                
                # 方法1: 从几何信息计算
                if hasattr(entity.dxf, 'defpoint') and hasattr(entity.dxf, 'defpoint2'):
                    p1 = entity.dxf.defpoint
                    p2 = entity.dxf.defpoint2
                    value = ((p2.x - p1.x)**2 + (p2.y - p1.y)**2)**0.5
                
                # 方法2: 获取测量值
                elif hasattr(entity.dxf, 'measurement') and entity.dxf.measurement > 0:
                    value = entity.dxf.measurement
                
                if value is not None:
                    dim_info = {
                        'value': round(value, 3),
                        'layer': getattr(entity.dxf, 'layer', 'Unknown'),
                        'text': getattr(entity.dxf, 'text', ''),
                        'position': None
                    }
                    
                    # 获取位置
                    if hasattr(entity.dxf, 'defpoint'):
                        dim_info['position'] = [
                            round(entity.dxf.defpoint.x, 2),
                            round(entity.dxf.defpoint.y, 2)
                        ]
                    
                    dimensions.append(dim_info)
        
        return dimensions
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return []


def main():
    """主函数"""
    print("🔧 DXF尺寸数值提取器")
    print("=" * 30)
    
    # 查找DXF文件
    current_dir = Path('.')
    dxf_files = list(current_dir.glob('*.dxf')) + list(current_dir.glob('*.DXF'))
    
    if not dxf_files:
        print("❌ 当前目录下没有找到DXF文件")
        return
    
    all_results = {}
    all_values = []
    
    for file_path in dxf_files:
        print(f"\n📁 处理: {file_path.name}")
        
        dimensions = extract_dimension_values(str(file_path))
        
        if dimensions:
            values = [dim['value'] for dim in dimensions]
            all_values.extend(values)
            all_results[file_path.name] = {
                'dimensions': dimensions,
                'values': values,
                'count': len(dimensions)
            }
            
            print(f"✅ 找到 {len(dimensions)} 个尺寸标注")
            print(f"📏 尺寸数值: {values}")
            
            # 显示详细信息
            for i, dim in enumerate(dimensions, 1):
                print(f"   {i}. {dim['value']} (图层: {dim['layer']}, 位置: {dim['position']})")
        else:
            print("❌ 没有找到有效的尺寸标注")
    
    # 总结
    if all_values:
        print(f"\n{'='*50}")
        print(f"📊 总结:")
        print(f"   总文件数: {len([f for f in all_results if all_results[f]['count'] > 0])}")
        print(f"   总标注数: {len(all_values)}")
        print(f"   数值范围: {min(all_values):.3f} ~ {max(all_values):.3f}")
        print(f"   平均值: {sum(all_values)/len(all_values):.3f}")
        print(f"   所有数值: {sorted(set(all_values))}")  # 去重并排序
        
        # 保存结果到JSON文件
        output_file = 'dimension_values.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        print(f"💾 结果已保存到: {output_file}")
    else:
        print("\n❌ 没有提取到任何尺寸数值")


if __name__ == "__main__":
    main()
