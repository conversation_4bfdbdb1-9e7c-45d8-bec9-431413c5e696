#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的DXF文件标注提取示例
这个脚本展示了如何提取DXF文件中的基本标注信息
"""

import sys
from pathlib import Path

try:
    import ezdxf
except ImportError:
    print("请先安装ezdxf库: pip install ezdxf")
    sys.exit(1)


def extract_basic_dimensions(file_path: str):
    """提取DXF文件中的基本标注信息"""
    try:
        # 读取DXF文件
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        
        print(f"正在分析文件: {file_path}")
        print("-" * 50)
        
        # 统计各种实体类型
        entity_counts = {}
        dimensions = []
        texts = []
        
        for entity in msp:
            entity_type = entity.dxftype()
            entity_counts[entity_type] = entity_counts.get(entity_type, 0) + 1
            
            # 收集尺寸标注
            if 'DIMENSION' in entity_type or entity_type in ['ALIGNED', 'LINEAR', 'RADIAL', 'ANGULAR']:
                dim_data = {
                    'type': entity_type,
                    'layer': getattr(entity.dxf, 'layer', 'Unknown')
                }
                
                # 尝试获取测量值
                if hasattr(entity.dxf, 'measurement'):
                    dim_data['measurement'] = entity.dxf.measurement
                
                # 尝试获取标注文本
                if hasattr(entity.dxf, 'text'):
                    dim_data['text'] = entity.dxf.text
                
                dimensions.append(dim_data)
            
            # 收集文本
            elif entity_type in ['TEXT', 'MTEXT']:
                text_data = {
                    'type': entity_type,
                    'layer': getattr(entity.dxf, 'layer', 'Unknown')
                }
                
                if hasattr(entity.dxf, 'text'):
                    text_data['text'] = entity.dxf.text
                elif hasattr(entity, 'plain_text'):
                    text_data['text'] = entity.plain_text()
                
                if text_data.get('text'):
                    texts.append(text_data)
        
        # 打印统计信息
        print("实体类型统计:")
        for entity_type, count in sorted(entity_counts.items()):
            print(f"  {entity_type}: {count}")
        
        print(f"\n找到 {len(dimensions)} 个尺寸标注:")
        for i, dim in enumerate(dimensions, 1):
            print(f"  {i}. {dim['type']} (图层: {dim['layer']})")
            if 'measurement' in dim:
                print(f"     测量值: {dim['measurement']}")
            if 'text' in dim:
                print(f"     文本: {dim['text']}")
        
        print(f"\n找到 {len(texts)} 个文本标注:")
        for i, text in enumerate(texts, 1):
            print(f"  {i}. {text['type']} (图层: {text['layer']})")
            print(f"     内容: {text['text']}")
        
        return {
            'dimensions': dimensions,
            'texts': texts,
            'entity_counts': entity_counts
        }
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None


def main():
    """主函数"""
    print("DXF文件标注提取示例")
    print("=" * 50)
    
    # 查找当前目录下的DXF文件
    current_dir = Path('.')
    dxf_files = list(current_dir.glob('*.dxf')) + list(current_dir.glob('*.DXF'))
    
    if not dxf_files:
        print("当前目录下没有找到DXF文件")
        return
    
    print(f"找到 {len(dxf_files)} 个DXF文件:")
    for file_path in dxf_files:
        print(f"  - {file_path.name}")
    
    print("\n开始处理文件...")
    
    # 处理每个DXF文件
    for file_path in dxf_files:
        print(f"\n{'='*60}")
        result = extract_basic_dimensions(str(file_path))
        if result:
            print("✅ 处理完成")
        else:
            print("❌ 处理失败")


if __name__ == "__main__":
    main()
