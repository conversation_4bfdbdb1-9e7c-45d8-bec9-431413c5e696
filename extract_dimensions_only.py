#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门提取DXF文件中尺寸标注的工具
只关注尺寸标注，不处理文本标注
"""

import sys
from pathlib import Path
from typing import List, Dict, Any

try:
    import ezdxf
except ImportError:
    print("请先安装ezdxf库: pip install ezdxf")
    sys.exit(1)


class DimensionOnlyExtractor:
    """专门提取尺寸标注的工具"""
    
    def __init__(self):
        self.supported_formats = ['.dxf', '.DXF']
    
    def load_dxf_file(self, file_path: str):
        """加载DXF文件"""
        try:
            return ezdxf.readfile(file_path)
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            return None
    
    def extract_dimensions(self, doc) -> List[Dict[str, Any]]:
        """提取尺寸标注"""
        dimensions = []
        msp = doc.modelspace()
        
        for entity in msp:
            if entity.dxftype() == 'DIMENSION':
                dim_info = self._parse_dimension(entity)
                if dim_info:
                    dimensions.append(dim_info)
        
        return dimensions
    
    def _parse_dimension(self, entity) -> Dict[str, Any]:
        """解析单个尺寸标注"""
        try:
            dim_info = {
                'type': 'DIMENSION',
                'layer': getattr(entity.dxf, 'layer', 'Unknown'),
                'measurement': None,
                'text': None,
                'position': None,
                'calculated_value': None
            }

            # 获取实际测量值
            if hasattr(entity.dxf, 'measurement'):
                dim_info['measurement'] = entity.dxf.measurement

            # 获取标注文本
            if hasattr(entity.dxf, 'text'):
                dim_info['text'] = entity.dxf.text

            # 获取位置
            if hasattr(entity.dxf, 'defpoint'):
                dim_info['position'] = (entity.dxf.defpoint.x, entity.dxf.defpoint.y)
            elif hasattr(entity.dxf, 'insert'):
                dim_info['position'] = (entity.dxf.insert.x, entity.dxf.insert.y)

            # 尝试从标注几何信息计算尺寸
            dim_info['calculated_value'] = self._calculate_dimension_value(entity)

            # 获取更多标注属性
            dim_info['raw_attributes'] = {}
            for attr in dir(entity.dxf):
                if not attr.startswith('_'):
                    try:
                        value = getattr(entity.dxf, attr)
                        if isinstance(value, (int, float, str)):
                            dim_info['raw_attributes'][attr] = value
                    except:
                        pass

            return dim_info

        except Exception as e:
            print(f"⚠️  解析标注失败: {e}")
            return None

    def _calculate_dimension_value(self, entity):
        """尝试从标注的几何信息计算实际尺寸值"""
        try:
            # 尝试获取标注的关键点
            if hasattr(entity.dxf, 'defpoint') and hasattr(entity.dxf, 'defpoint2'):
                p1 = entity.dxf.defpoint
                p2 = entity.dxf.defpoint2
                # 计算两点间距离
                distance = ((p2.x - p1.x)**2 + (p2.y - p1.y)**2)**0.5
                return distance

            # 尝试其他方法获取尺寸
            if hasattr(entity.dxf, 'actual_measurement'):
                return entity.dxf.actual_measurement

            return None
        except:
            return None
    
    def get_dimension_values(self, dimensions: List[Dict[str, Any]]) -> List[float]:
        """获取所有尺寸标注的数值"""
        values = []
        for dim in dimensions:
            # 优先使用measurement，其次使用calculated_value
            if dim.get('measurement') is not None:
                values.append(dim['measurement'])
            elif dim.get('calculated_value') is not None:
                values.append(dim['calculated_value'])
        return values
    
    def process_file(self, file_path: str):
        """处理DXF文件并提取尺寸标注"""
        print(f"📁 处理文件: {file_path}")
        
        # 加载文件
        doc = self.load_dxf_file(file_path)
        if not doc:
            return None
        
        # 提取尺寸标注
        dimensions = self.extract_dimensions(doc)
        
        print(f"✅ 找到 {len(dimensions)} 个尺寸标注")
        
        # 显示详细信息
        for i, dim in enumerate(dimensions, 1):
            print(f"\n📏 标注 {i}:")
            print(f"   图层: {dim['layer']}")
            
            if dim['measurement'] is not None:
                print(f"   测量值: {dim['measurement']:.3f}")
            else:
                print(f"   测量值: 未获取到")
            
            if dim['text']:
                print(f"   显示文本: {dim['text']}")
            
            if dim['position']:
                print(f"   位置: ({dim['position'][0]:.2f}, {dim['position'][1]:.2f})")
        
        # 获取所有数值
        values = self.get_dimension_values(dimensions)
        if values:
            print(f"\n📊 所有尺寸数值:")
            print(f"   数值列表: {values}")
            print(f"   最小值: {min(values):.3f}")
            print(f"   最大值: {max(values):.3f}")
            print(f"   平均值: {sum(values)/len(values):.3f}")
        else:
            print(f"\n⚠️  没有获取到有效的测量数值")
        
        return {
            'dimensions': dimensions,
            'values': values,
            'count': len(dimensions)
        }


def main():
    """主函数"""
    print("🔧 DXF尺寸标注提取工具")
    print("=" * 40)
    
    extractor = DimensionOnlyExtractor()
    current_dir = Path('.')
    
    # 查找DXF文件
    dxf_files = list(current_dir.glob('*.dxf')) + list(current_dir.glob('*.DXF'))
    
    if not dxf_files:
        print("❌ 当前目录下没有找到DXF文件")
        return
    
    print(f"📂 找到 {len(dxf_files)} 个DXF文件:")
    for file_path in dxf_files:
        print(f"   - {file_path.name}")
    
    # 处理每个文件
    all_values = []
    for file_path in dxf_files:
        print(f"\n{'='*60}")
        result = extractor.process_file(str(file_path))
        if result and result['values']:
            all_values.extend(result['values'])
    
    # 总结所有文件的尺寸数值
    if all_values:
        print(f"\n{'='*60}")
        print(f"📈 所有文件的尺寸统计:")
        print(f"   总标注数: {len(all_values)}")
        print(f"   数值范围: {min(all_values):.3f} ~ {max(all_values):.3f}")
        print(f"   平均值: {sum(all_values)/len(all_values):.3f}")
        print(f"   所有数值: {sorted(all_values)}")


if __name__ == "__main__":
    main()
