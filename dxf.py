#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF/DWG文件标注值提取工具
支持提取各种类型的标注信息，包括尺寸标注、文本标注等
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional

try:
    import ezdxf
    print("✓ ezdxf库已安装")
except ImportError:
    print("❌ 需要安装ezdxf库: pip install ezdxf")
    sys.exit(1)

try:
    import ezdxf.addons.odafc as odafc
    ODA_AVAILABLE = True
    print("✓ ODA文件转换器可用 (支持DWG)")
except ImportError:
    ODA_AVAILABLE = False
    print("⚠️  ODA文件转换器不可用，仅支持DXF文件")


class DimensionExtractor:
    """DXF/DWG文件标注提取器"""

    def __init__(self):
        self.supported_formats = ['.dxf', '.DXF']
        if ODA_AVAILABLE:
            self.supported_formats.extend(['.dwg', '.DWG'])

    def is_supported_file(self, file_path: str) -> bool:
        """检查文件格式是否支持"""
        return Path(file_path).suffix in self.supported_formats

    def load_drawing(self, file_path: str) -> Optional[ezdxf.document.Drawing]:
        """加载DXF/DWG文件"""
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                print(f"❌ 文件不存在: {file_path}")
                return None

            if file_path.suffix.lower() == '.dwg':
                if not ODA_AVAILABLE:
                    print("❌ DWG文件需要ODA文件转换器支持")
                    return None

                # 将DWG转换为DXF
                temp_dxf = file_path.with_suffix('.temp.dxf')
                try:
                    odafc.readfile(str(file_path), str(temp_dxf))
                    doc = ezdxf.readfile(str(temp_dxf))
                    temp_dxf.unlink()  # 删除临时文件
                    return doc
                except Exception as e:
                    print(f"❌ DWG文件转换失败: {e}")
                    if temp_dxf.exists():
                        temp_dxf.unlink()
                    return None
            else:
                # 直接读取DXF文件
                return ezdxf.readfile(str(file_path))

        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            return None

    def extract_dimensions(self, doc: ezdxf.document.Drawing) -> List[Dict[str, Any]]:
        """提取尺寸标注"""
        dimensions = []
        msp = doc.modelspace()

        # 提取各种类型的尺寸标注
        dimension_types = [
            'DIMENSION',      # 通用尺寸标注
            'ALIGNED',        # 对齐标注
            'LINEAR',         # 线性标注
            'RADIAL',         # 径向标注
            'DIAMETRIC',      # 直径标注
            'ANGULAR',        # 角度标注
            'ANGULAR3P',      # 三点角度标注
            'ORDINATE',       # 坐标标注
        ]

        for entity in msp:
            if entity.dxftype() in dimension_types:
                dim_info = self._parse_dimension_entity(entity)
                if dim_info:
                    dimensions.append(dim_info)

        return dimensions

    def _parse_dimension_entity(self, entity) -> Optional[Dict[str, Any]]:
        """解析尺寸标注实体"""
        try:
            dim_info = {
                'type': entity.dxftype(),
                'layer': getattr(entity.dxf, 'layer', 'Unknown'),
                'measurement': None,
                'text': None,
                'position': None,
                'raw_data': {}
            }

            # 获取测量值
            if hasattr(entity.dxf, 'measurement'):
                dim_info['measurement'] = entity.dxf.measurement

            # 获取标注文本
            if hasattr(entity.dxf, 'text'):
                dim_info['text'] = entity.dxf.text

            # 获取位置信息
            if hasattr(entity.dxf, 'defpoint'):
                dim_info['position'] = (entity.dxf.defpoint.x, entity.dxf.defpoint.y)
            elif hasattr(entity.dxf, 'insert'):
                dim_info['position'] = (entity.dxf.insert.x, entity.dxf.insert.y)

            # 获取其他相关属性
            for attr in ['dimstyle', 'dimscale', 'dimtxt']:
                if hasattr(entity.dxf, attr):
                    dim_info['raw_data'][attr] = getattr(entity.dxf, attr)

            return dim_info

        except Exception as e:
            print(f"⚠️  解析标注实体失败: {e}")
            return None