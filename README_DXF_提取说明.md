# DXF/DWG文件标注值提取工具

## 概述

这个工具集可以从AutoCAD的DXF和DWG文件中提取各种类型的标注信息，包括尺寸标注、文本标注等。

## 处理流程

### 1. 文件格式支持
- **DXF文件**: 直接支持，无需额外配置
- **DWG文件**: 需要安装ODA文件转换器（可选）

### 2. 标注类型识别
- **尺寸标注 (DIMENSION)**: 线性、角度、径向、直径等各种尺寸标注
- **文本标注 (TEXT/MTEXT)**: 单行文本和多行文本
- **属性标注 (ATTRIB/ATTDEF)**: 块属性和属性定义

### 3. 提取信息
- 标注的测量值
- 标注文本内容
- 标注位置坐标
- 所在图层信息
- 文本样式和高度

## 文件说明

### 核心文件

1. **`dxf_dimension_extractor.py`** - 完整功能的提取器
   - 支持DXF和DWG文件
   - 详细的标注信息提取
   - 完整的错误处理

2. **`extract_dimensions_example.py`** - 简化示例
   - 仅支持DXF文件
   - 基本的标注提取功能
   - 适合学习和快速测试

3. **`install_dependencies.py`** - 依赖安装脚本
   - 自动安装所需的Python库
   - 检查安装状态

## 使用方法

### 1. 安装依赖
```bash
python install_dependencies.py
```

### 2. 运行完整版提取器
```bash
python dxf_dimension_extractor.py
```

### 3. 运行简化版示例
```bash
python extract_dimensions_example.py
```

## 提取结果示例

### 尺寸标注信息
```
📏 尺寸标注详情:
   1. 类型: DIMENSION
      图层: 10
      标注文本: <>
      位置: (523.67, 481.55)
```

### 文本标注信息
```
📝 文本标注详情:
   1. 类型: MTEXT
      图层: 10
      文本内容: 149
      提取数值: [149.0]
      位置: (1113.21, 35.16)
```

## 技术特点

### 1. 智能数值提取
- 自动从文本中提取数字
- 支持整数和小数
- 支持负数

### 2. 多图层支持
- 识别不同图层的标注
- 按图层分类显示结果

### 3. 位置信息
- 提供标注的精确坐标
- 便于定位和验证

### 4. 错误处理
- 完善的异常处理机制
- 详细的错误信息提示

## 代码结构

### DimensionExtractor类主要方法

```python
class DimensionExtractor:
    def load_drawing(file_path)          # 加载DXF/DWG文件
    def extract_dimensions(doc)          # 提取尺寸标注
    def extract_text_annotations(doc)    # 提取文本标注
    def extract_numeric_values(text)     # 从文本提取数值
    def process_file(file_path)          # 处理单个文件
    def print_results(result)            # 打印结果
```

## 依赖库

- **ezdxf**: DXF文件处理的主要库
- **ezdxf.addons.odafc**: DWG文件支持（可选）

## 注意事项

### 1. DWG文件支持
- 需要安装ODA文件转换器
- 下载地址: https://www.opendesign.com/guestfiles/oda_file_converter
- 如果没有安装，只能处理DXF文件

### 2. 中文字符处理
- 工具可以正确处理中文字符
- Unicode编码会自动转换显示

### 3. 标注文本格式
- `<>` 表示使用测量值
- `%%c` 表示直径符号
- 其他特殊符号会按原样显示

## 扩展功能

### 1. 自定义过滤
可以根据需要修改代码来过滤特定类型的标注：

```python
# 只提取特定图层的标注
if entity.dxf.layer in ['10', 'DIMENSION']:
    # 处理标注
```

### 2. 导出功能
可以将提取结果导出为CSV或JSON格式：

```python
import json
with open('dimensions.json', 'w') as f:
    json.dump(result, f, ensure_ascii=False, indent=2)
```

### 3. 批量处理
可以扩展为处理整个目录的所有DXF/DWG文件。

## 故障排除

### 1. 导入错误
```
ImportError: No module named 'ezdxf'
```
解决方案: 运行 `python install_dependencies.py`

### 2. DWG文件无法打开
```
Could not find ODAFileConverter in the path
```
解决方案: 安装ODA文件转换器或使用DXF格式

### 3. 文件损坏
```
DXFStructureError: Invalid DXF file
```
解决方案: 检查文件完整性，尝试在AutoCAD中重新保存

## 联系支持

如果遇到问题或需要功能扩展，请检查：
1. Python版本兼容性（推荐Python 3.7+）
2. 文件格式正确性
3. 依赖库版本
