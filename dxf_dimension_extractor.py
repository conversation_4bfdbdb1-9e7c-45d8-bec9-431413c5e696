#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DXF/DWG文件标注值提取工具
支持提取各种类型的标注信息，包括尺寸标注、文本标注等
"""

import re
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional

try:
    import ezdxf
    print("✓ ezdxf库已安装")
except ImportError:
    print("❌ 需要安装ezdxf库: pip install ezdxf")
    sys.exit(1)

try:
    import ezdxf.addons.odafc as odafc
    ODA_AVAILABLE = True
    print("✓ ODA文件转换器可用 (支持DWG)")
except ImportError:
    ODA_AVAILABLE = False
    print("⚠️  ODA文件转换器不可用，仅支持DXF文件")


class DimensionExtractor:
    """DXF/DWG文件标注提取器"""
    
    def __init__(self):
        self.supported_formats = ['.dxf', '.DXF']
        if ODA_AVAILABLE:
            self.supported_formats.extend(['.dwg', '.DWG'])
    
    def is_supported_file(self, file_path: str) -> bool:
        """检查文件格式是否支持"""
        return Path(file_path).suffix in self.supported_formats
    
    def load_drawing(self, file_path: str) -> Optional[ezdxf.document.Drawing]:
        """加载DXF/DWG文件"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                print(f"❌ 文件不存在: {file_path}")
                return None
            
            if file_path.suffix.lower() == '.dwg':
                if not ODA_AVAILABLE:
                    print("❌ DWG文件需要ODA文件转换器支持")
                    return None
                
                # 将DWG转换为DXF
                temp_dxf = file_path.with_suffix('.temp.dxf')
                try:
                    odafc.readfile(str(file_path), str(temp_dxf))
                    doc = ezdxf.readfile(str(temp_dxf))
                    temp_dxf.unlink()  # 删除临时文件
                    return doc
                except Exception as e:
                    print(f"❌ DWG文件转换失败: {e}")
                    if temp_dxf.exists():
                        temp_dxf.unlink()
                    return None
            else:
                # 直接读取DXF文件
                return ezdxf.readfile(str(file_path))
                
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            return None
    
    def extract_dimensions(self, doc: ezdxf.document.Drawing) -> List[Dict[str, Any]]:
        """提取尺寸标注"""
        dimensions = []
        msp = doc.modelspace()
        
        # 提取各种类型的尺寸标注
        dimension_types = [
            'DIMENSION',      # 通用尺寸标注
            'ALIGNED',        # 对齐标注
            'LINEAR',         # 线性标注
            'RADIAL',         # 径向标注
            'DIAMETRIC',      # 直径标注
            'ANGULAR',        # 角度标注
            'ANGULAR3P',      # 三点角度标注
            'ORDINATE',       # 坐标标注
        ]
        
        for entity in msp:
            if entity.dxftype() in dimension_types:
                dim_info = self._parse_dimension_entity(entity)
                if dim_info:
                    dimensions.append(dim_info)
        
        return dimensions
    
    def _parse_dimension_entity(self, entity) -> Optional[Dict[str, Any]]:
        """解析尺寸标注实体"""
        try:
            dim_info = {
                'type': entity.dxftype(),
                'layer': getattr(entity.dxf, 'layer', 'Unknown'),
                'measurement': None,
                'text': None,
                'position': None,
                'raw_data': {}
            }
            
            # 获取测量值
            if hasattr(entity.dxf, 'measurement'):
                dim_info['measurement'] = entity.dxf.measurement
            
            # 获取标注文本
            if hasattr(entity.dxf, 'text'):
                dim_info['text'] = entity.dxf.text
            
            # 获取位置信息
            if hasattr(entity.dxf, 'defpoint'):
                dim_info['position'] = (entity.dxf.defpoint.x, entity.dxf.defpoint.y)
            elif hasattr(entity.dxf, 'insert'):
                dim_info['position'] = (entity.dxf.insert.x, entity.dxf.insert.y)
            
            # 获取其他相关属性
            for attr in ['dimstyle', 'dimscale', 'dimtxt']:
                if hasattr(entity.dxf, attr):
                    dim_info['raw_data'][attr] = getattr(entity.dxf, attr)
            
            return dim_info
            
        except Exception as e:
            print(f"⚠️  解析标注实体失败: {e}")
            return None
    
    def extract_text_annotations(self, doc: ezdxf.document.Drawing) -> List[Dict[str, Any]]:
        """提取文本标注"""
        text_annotations = []
        msp = doc.modelspace()
        
        # 提取文本实体
        text_types = ['TEXT', 'MTEXT', 'ATTRIB', 'ATTDEF']
        
        for entity in msp:
            if entity.dxftype() in text_types:
                text_info = self._parse_text_entity(entity)
                if text_info:
                    text_annotations.append(text_info)
        
        return text_annotations
    
    def _parse_text_entity(self, entity) -> Optional[Dict[str, Any]]:
        """解析文本实体"""
        try:
            text_info = {
                'type': entity.dxftype(),
                'layer': getattr(entity.dxf, 'layer', 'Unknown'),
                'text': None,
                'position': None,
                'height': None,
                'rotation': None,
                'style': None
            }
            
            # 获取文本内容
            if hasattr(entity.dxf, 'text'):
                text_info['text'] = entity.dxf.text
            elif hasattr(entity, 'plain_text'):
                text_info['text'] = entity.plain_text()
            
            # 获取位置
            if hasattr(entity.dxf, 'insert'):
                text_info['position'] = (entity.dxf.insert.x, entity.dxf.insert.y)
            
            # 获取其他属性
            for attr, key in [('height', 'height'), ('rotation', 'rotation'), ('style', 'style')]:
                if hasattr(entity.dxf, attr):
                    text_info[key] = getattr(entity.dxf, attr)
            
            return text_info
            
        except Exception as e:
            print(f"⚠️  解析文本实体失败: {e}")
            return None
    
    def extract_numeric_values(self, text: str) -> List[float]:
        """从文本中提取数值"""
        if not text:
            return []
        
        # 匹配数字模式（包括小数、负数）
        number_pattern = r'-?\d+\.?\d*'
        matches = re.findall(number_pattern, text)
        
        numbers = []
        for match in matches:
            try:
                numbers.append(float(match))
            except ValueError:
                continue
        
        return numbers

    def process_file(self, file_path: str) -> Dict[str, Any]:
        """处理单个文件"""
        print(f"\n📁 处理文件: {file_path}")

        if not self.is_supported_file(file_path):
            print(f"❌ 不支持的文件格式: {Path(file_path).suffix}")
            return {'error': '不支持的文件格式'}

        # 加载文件
        doc = self.load_drawing(file_path)
        if not doc:
            return {'error': '文件加载失败'}

        # 提取标注信息
        dimensions = self.extract_dimensions(doc)
        text_annotations = self.extract_text_annotations(doc)

        # 统计信息
        result = {
            'file_path': file_path,
            'dimensions': dimensions,
            'text_annotations': text_annotations,
            'summary': {
                'total_dimensions': len(dimensions),
                'total_text_annotations': len(text_annotations),
                'layers': set()
            }
        }

        # 收集图层信息
        for item in dimensions + text_annotations:
            if item.get('layer'):
                result['summary']['layers'].add(item['layer'])

        result['summary']['layers'] = list(result['summary']['layers'])

        return result

    def print_results(self, result: Dict[str, Any]):
        """打印提取结果"""
        if 'error' in result:
            print(f"❌ 错误: {result['error']}")
            return

        print(f"\n📊 提取结果汇总:")
        print(f"   文件: {result['file_path']}")
        print(f"   尺寸标注: {result['summary']['total_dimensions']} 个")
        print(f"   文本标注: {result['summary']['total_text_annotations']} 个")
        print(f"   图层: {', '.join(result['summary']['layers'])}")

        # 打印尺寸标注详情
        if result['dimensions']:
            print(f"\n📏 尺寸标注详情:")
            for i, dim in enumerate(result['dimensions'], 1):
                print(f"   {i}. 类型: {dim['type']}")
                print(f"      图层: {dim['layer']}")
                if dim['measurement'] is not None:
                    print(f"      测量值: {dim['measurement']}")
                if dim['text']:
                    print(f"      标注文本: {dim['text']}")
                    numbers = self.extract_numeric_values(dim['text'])
                    if numbers:
                        print(f"      提取数值: {numbers}")
                if dim['position']:
                    print(f"      位置: ({dim['position'][0]:.2f}, {dim['position'][1]:.2f})")
                print()

        # 打印文本标注详情
        if result['text_annotations']:
            print(f"\n📝 文本标注详情:")
            for i, text in enumerate(result['text_annotations'], 1):
                print(f"   {i}. 类型: {text['type']}")
                print(f"      图层: {text['layer']}")
                if text['text']:
                    print(f"      文本内容: {text['text']}")
                    numbers = self.extract_numeric_values(text['text'])
                    if numbers:
                        print(f"      提取数值: {numbers}")
                if text['position']:
                    print(f"      位置: ({text['position'][0]:.2f}, {text['position'][1]:.2f})")
                if text['height']:
                    print(f"      字体高度: {text['height']}")
                print()


def main():
    """主函数"""
    print("🔧 DXF/DWG文件标注值提取工具")
    print("=" * 50)

    extractor = DimensionExtractor()
    current_dir = Path('.')

    # 查找当前目录下的DXF/DWG文件
    supported_files = []
    for file_path in current_dir.iterdir():
        if file_path.is_file() and extractor.is_supported_file(str(file_path)):
            supported_files.append(file_path)

    if not supported_files:
        print("❌ 当前目录下没有找到支持的DXF/DWG文件")
        return

    print(f"📂 找到 {len(supported_files)} 个支持的文件:")
    for file_path in supported_files:
        print(f"   - {file_path.name}")

    # 处理每个文件
    for file_path in supported_files:
        result = extractor.process_file(str(file_path))
        extractor.print_results(result)


if __name__ == "__main__":
    main()
